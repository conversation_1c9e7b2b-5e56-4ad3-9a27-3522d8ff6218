const { r2rClient } = require("r2r-js");

const client = new r2rClient("http://192.168.0.115:7272");

async function main() {
    const EMAIL = "<EMAIL>";
    const PASSWORD = "120784060";
    console.log("Logging in...");
    const res = await client.users.login({email: EMAIL, password: PASSWORD});
    const user_id = (await client.users.me()).results.id;

    const response = await client.conversations.list({offset: 0, limit: 100});
    let conv;
    let result;
    for (const i in response.results)
    {
        conv = response.results[i];
        if (conv.userId === user_id)
        {
            console.log(conv.name, " ID:", conv.id)
            result = await client.conversations.retrieve({id: conv.id});
            let msg;
            for (const j in result.results)
            {
                msg = result.results[j];
                console.log("    ", msg.message.role, ": ", msg.message.content)
            }
        }
    }
}

main();

