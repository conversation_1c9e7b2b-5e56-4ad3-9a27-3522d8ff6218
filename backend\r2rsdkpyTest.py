from r2r import R2RClient

client = R2RClient("http://192.168.0.115:7272")

tokens = client.users.login(
    email="<EMAIL>",
    password="120784060"
)

# Get current user ID
user_id = client.users.me().results.id
print(user_id)

# List conversations
result = client.conversations.list(
    offset=0,
    limit=10,
)

# Print details of each conversation of the current user
for conv in result.results:
    if conv.user_id == user_id:
        print(conv)
        result = client.conversations.retrieve(conv.id)
        for msg in result.results:
            print("    ", msg.message.role, ": ", msg.message.content)

# print(result)


